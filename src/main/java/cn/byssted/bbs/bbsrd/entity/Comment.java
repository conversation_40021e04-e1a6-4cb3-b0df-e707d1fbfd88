package cn.byssted.bbs.bbsrd.entity;

import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;

/**
 * 评论实体类
 */
@TableName("comments")
public class Comment {
    
    @TableId(value = "comment_id", type = IdType.AUTO)
    private Integer commentId;
    
    @TableField("post_id")
    private Integer postId;
    
    @TableField("user_id")
    private Integer userId;
    
    @TableField("parent_comment_id")
    private Integer parentCommentId;
    
    @TableField("content")
    private String content;
    
    @TableField("created_at")
    private LocalDateTime createdAt;
    
    @TableField("upvotes")
    private Integer upvotes;
    
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
    
    // 构造函数、getter和setter方法
}
