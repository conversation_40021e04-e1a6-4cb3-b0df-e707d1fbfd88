package cn.byssted.bbs.bbsrd.controller;

import cn.byssted.bbs.bbsrd.common.Result;
import cn.byssted.bbs.bbsrd.service.PostService;
import cn.byssted.bbs.bbsrd.service.UserService;
import cn.byssted.bbs.bbsrd.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 管理员控制器
 */
@RestController
@RequestMapping("/api/admin")
@CrossOrigin(origins = "*")
public class AdminController {
    
    @Autowired
    private PostService postService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    /**
     * 检查管理员权限
     */
    private boolean checkAdminPermission(String token) {
        if (token == null || !token.startsWith("Bearer ")) {
            return false;
        }
        
        try {
            String jwtToken = token.substring(7);
            String isAdmin = jwtUtil.getIsAdminFromToken(jwtToken);
            return "1".equals(isAdmin);
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 置顶帖子
     */
    @PutMapping("/posts/{id}/pin")
    public Result<String> pinPost(@RequestHeader("Authorization") String token,
                                 @PathVariable Long id,
                                 @RequestBody Map<String, Integer> request) {
        try {
            if (!checkAdminPermission(token)) {
                return Result.forbidden("只有管理员可以置顶帖子");
            }
            
            Integer isPinned = request.get("isPinned");
            if (isPinned == null) {
                return Result.badRequest("置顶状态不能为空");
            }
            
            boolean success = postService.pinPost(id, isPinned);
            if (success) {
                String message = isPinned == 1 ? "置顶成功" : "取消置顶成功";
                return Result.success(message);
            } else {
                return Result.error("操作失败");
            }
        } catch (Exception e) {
            return Result.error("置顶操作失败：" + e.getMessage());
        }
    }
    
    /**
     * 加精帖子
     */
    @PutMapping("/posts/{id}/feature")
    public Result<String> featurePost(@RequestHeader("Authorization") String token,
                                     @PathVariable Long id,
                                     @RequestBody Map<String, Integer> request) {
        try {
            if (!checkAdminPermission(token)) {
                return Result.forbidden("只有管理员可以加精帖子");
            }
            
            Integer isFeatured = request.get("isFeatured");
            if (isFeatured == null) {
                return Result.badRequest("加精状态不能为空");
            }
            
            boolean success = postService.featurePost(id, isFeatured);
            if (success) {
                String message = isFeatured == 1 ? "加精成功" : "取消加精成功";
                
                // 如果是加精，给帖子作者奖励100积分
                if (isFeatured == 1) {
                    // 这里需要获取帖子作者ID并奖励积分
                    // 为了简化，暂时不实现，可以后续添加
                }
                
                return Result.success(message);
            } else {
                return Result.error("操作失败");
            }
        } catch (Exception e) {
            return Result.error("加精操作失败：" + e.getMessage());
        }
    }
    
    /**
     * 调整用户积分
     */
    @PutMapping("/users/{id}/points")
    public Result<String> adjustUserPoints(@RequestHeader("Authorization") String token,
                                          @PathVariable Long id,
                                          @RequestBody Map<String, Integer> request) {
        try {
            if (!checkAdminPermission(token)) {
                return Result.forbidden("只有管理员可以调整用户积分");
            }
            
            Integer points = request.get("points");
            if (points == null) {
                return Result.badRequest("积分变化量不能为空");
            }
            
            boolean success = userService.adjustUserPoints(id, points);
            if (success) {
                String message = points > 0 ? "积分奖励成功" : "积分扣除成功";
                return Result.success(message);
            } else {
                return Result.error("操作失败");
            }
        } catch (Exception e) {
            return Result.error("积分调整失败：" + e.getMessage());
        }
    }
}
