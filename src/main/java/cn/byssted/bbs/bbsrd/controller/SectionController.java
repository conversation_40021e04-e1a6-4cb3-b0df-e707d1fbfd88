package cn.byssted.bbs.bbsrd.controller;

import cn.byssted.bbs.bbsrd.common.Result;
import cn.byssted.bbs.bbsrd.entity.Section;
import cn.byssted.bbs.bbsrd.service.SectionService;
import cn.byssted.bbs.bbsrd.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 板块控制器
 */
@RestController
@RequestMapping("/api/sections")
@CrossOrigin(origins = "*")
public class SectionController {
    
    @Autowired
    private SectionService sectionService;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    /**
     * 获取所有板块
     */
    @GetMapping
    public Result<List<Section>> getAllSections() {
        try {
            List<Section> sections = sectionService.getAllSections();
            return Result.success(sections);
        } catch (Exception e) {
            return Result.error("获取板块列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID获取板块
     */
    @GetMapping("/{id}")
    public Result<Section> getSectionById(@PathVariable Integer id) {
        try {
            Section section = sectionService.getSectionById(id);
            if (section == null) {
                return Result.notFound("板块不存在");
            }
            return Result.success(section);
        } catch (Exception e) {
            return Result.error("获取板块信息失败：" + e.getMessage());
        }
    }
    
    /**
     * 创建板块（管理员功能）
     */
    @PostMapping
    public Result<Section> createSection(@RequestHeader("Authorization") String token,
                                        @RequestBody Map<String, String> request) {
        try {
            if (token == null || !token.startsWith("Bearer ")) {
                return Result.unauthorized("请先登录");
            }
            
            String jwtToken = token.substring(7);
            String isAdmin = jwtUtil.getIsAdminFromToken(jwtToken);
            
            if (!"1".equals(isAdmin)) {
                return Result.forbidden("只有管理员可以创建板块");
            }
            
            String name = request.get("name");
            String description = request.get("description");
            
            if (name == null || name.trim().isEmpty()) {
                return Result.badRequest("板块名称不能为空");
            }
            
            Section section = sectionService.createSection(name, description);
            return Result.success("创建成功", section);
        } catch (Exception e) {
            return Result.error("创建板块失败：" + e.getMessage());
        }
    }
    
    /**
     * 更新板块（管理员功能）
     */
    @PutMapping("/{id}")
    public Result<String> updateSection(@RequestHeader("Authorization") String token,
                                       @PathVariable Integer id,
                                       @RequestBody Map<String, String> request) {
        try {
            if (token == null || !token.startsWith("Bearer ")) {
                return Result.unauthorized("请先登录");
            }
            
            String jwtToken = token.substring(7);
            String isAdmin = jwtUtil.getIsAdminFromToken(jwtToken);
            
            if (!"1".equals(isAdmin)) {
                return Result.forbidden("只有管理员可以修改板块");
            }
            
            Section section = sectionService.getSectionById(id);
            if (section == null) {
                return Result.notFound("板块不存在");
            }
            
            String name = request.get("name");
            String description = request.get("description");
            
            if (name != null) {
                section.setName(name);
            }
            if (description != null) {
                section.setDescription(description);
            }
            
            boolean success = sectionService.updateSection(section);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            return Result.error("更新板块失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除板块（管理员功能）
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteSection(@RequestHeader("Authorization") String token,
                                       @PathVariable Integer id) {
        try {
            if (token == null || !token.startsWith("Bearer ")) {
                return Result.unauthorized("请先登录");
            }
            
            String jwtToken = token.substring(7);
            String isAdmin = jwtUtil.getIsAdminFromToken(jwtToken);
            
            if (!"1".equals(isAdmin)) {
                return Result.forbidden("只有管理员可以删除板块");
            }
            
            boolean success = sectionService.deleteSection(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            return Result.error("删除板块失败：" + e.getMessage());
        }
    }
}
